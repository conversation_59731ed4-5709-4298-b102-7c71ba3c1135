/**
 * @note
 * errno
 *
 * <AUTHOR>
 * @date 	2025-02-05
 */
package common

import (
	"gitlab.docsl.com/security/common"
)

// 将特定的errno注入ErrnoDesc
func init() {
	for k, v := range ErrnoDesc {
		common.ErrnoDesc[k] = v
	}
}

const (
	ErrQueryBiz = 10000
)

var ErrnoDesc = map[int]string{
	ErrQueryBiz: "请求业务列表失败",

	// System相关错误描述
	ErrQuerySystemList: "查询系统列表失败",
	ErrCreateSystem:    "创建系统失败",
	ErrUpdateSystem:    "更新系统失败",
	ErrDeleteSystem:    "删除系统失败",

	// Template相关错误描述
	ErrQueryTemplateList: "查询模板列表失败",
	ErrCreateTemplate:    "创建模板失败",
	ErrUpdateTemplate:    "更新模板失败",
	ErrDeleteTemplate:    "删除模板失败",

	// Flow相关错误描述
	ErrQueryFlowList: "查询流程列表失败",
	ErrGetFlow:       "获取流程失败",

	ErrWebAuthnValidateBegin:  "Webauthn令牌校验初始化失败",
	ErrWebAuthnValidateFinish: "Webauthn令牌校验失败",
	ErrWebAuthnSignupBegin:    "Webauthn令牌注册初始化失败",
	ErrWebAuthnSignupFinish:   "Webauthn令牌注册失败",
}
