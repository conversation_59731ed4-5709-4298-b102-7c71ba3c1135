/**
 * @note
 * biz logic structs
 *
 * <AUTHOR>
 * @date 	2025-09-02
 */
package biz

import "time"

// Biz相关结构体
type CreateBizRequest struct {
	Name             string `json:"name" binding:"required"`
	OriginBusinessID int64  `json:"originBusinessID"`
}

type UpdateBizRequest struct {
	BusinessID       string `json:"businessID" binding:"required"`
	Name             *string `json:"name"`
	OriginBusinessID *int64  `json:"originBusinessID"`
}

type ListBizRequest struct {
	Page             int    `json:"page" form:"page"`
	PerPage          int    `json:"perPage" form:"perPage"`
	BusinessID       string `json:"businessID" form:"businessID"`
	Name             string `json:"name" form:"name"`
	OriginBusinessID int64  `json:"originBusinessID" form:"originBusinessID"`
	Order            string `json:"order" form:"order"`
}

type BizResponse struct {
	ID               uint      `json:"id"`
	BusinessID       string    `json:"businessID"`
	Name             string    `json:"name"`
	OriginBusinessID int64     `json:"originBusinessID"`
	CreatedAt        time.Time `json:"createdAt"`
	UpdatedAt        time.Time `json:"updatedAt"`
}

type ListBizResponse struct {
	Items []*BizResponse `json:"items"`
	Total int64          `json:"total"`
	Page  int            `json:"page"`
	Size  int            `json:"size"`
}

// AlarmType相关结构体
type CreateAlarmTypeRequest struct {
	BusinessID   string `json:"businessID" binding:"required"`
	Name         string `json:"name" binding:"required"`
	OriginTypeID int64  `json:"originTypeID"`
}

type UpdateAlarmTypeRequest struct {
	TypeID       string  `json:"typeID" binding:"required"`
	BusinessID   *string `json:"businessID"`
	Name         *string `json:"name"`
	OriginTypeID *int64  `json:"originTypeID"`
}

type ListAlarmTypeRequest struct {
	Page         int    `json:"page" form:"page"`
	PerPage      int    `json:"perPage" form:"perPage"`
	TypeID       string `json:"typeID" form:"typeID"`
	BusinessID   string `json:"businessID" form:"businessID"`
	Name         string `json:"name" form:"name"`
	OriginTypeID int64  `json:"originTypeID" form:"originTypeID"`
	Order        string `json:"order" form:"order"`
}

type AlarmTypeResponse struct {
	ID           uint      `json:"id"`
	TypeID       string    `json:"typeID"`
	BusinessID   string    `json:"businessID"`
	Name         string    `json:"name"`
	OriginTypeID int64     `json:"originTypeID"`
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

type ListAlarmTypeResponse struct {
	Items []*AlarmTypeResponse `json:"items"`
	Total int64                `json:"total"`
	Page  int                  `json:"page"`
	Size  int                  `json:"size"`
}

// Tenant相关结构体
type CreateTenantRequest struct {
	Name       string `json:"name" binding:"required"`
	ExchangeID int64  `json:"exchangeID"`
}

type UpdateTenantRequest struct {
	TenantID   string `json:"tenantID" binding:"required"`
	Name       *string `json:"name"`
	ExchangeID *int64  `json:"exchangeID"`
}

type ListTenantRequest struct {
	Page       int    `json:"page" form:"page"`
	PerPage    int    `json:"perPage" form:"perPage"`
	TenantID   string `json:"tenantID" form:"tenantID"`
	Name       string `json:"name" form:"name"`
	ExchangeID int64  `json:"exchangeID" form:"exchangeID"`
	Order      string `json:"order" form:"order"`
}

type TenantResponse struct {
	ID         uint      `json:"id"`
	TenantID   string    `json:"tenantID"`
	Name       string    `json:"name"`
	ExchangeID int64     `json:"exchangeID"`
	CreatedAt  time.Time `json:"createdAt"`
	UpdatedAt  time.Time `json:"updatedAt"`
}

type ListTenantResponse struct {
	Items []*TenantResponse `json:"items"`
	Total int64             `json:"total"`
	Page  int               `json:"page"`
	Size  int               `json:"size"`
}
