/**
 * @note
 * biz logic
 *
 * <AUTHOR>
 * @date 	2025-09-02
 */
package biz

import (
	"context"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/idgen"
	bizModel "gitlab.docsl.com/security/socv2/soc/internal/model/biz"
)

// BizLogic 业务逻辑接口
type BizLogic interface {
	// Biz相关方法
	CreateBiz(ctx context.Context, req *CreateBizRequest) (*BizResponse, error)
	UpdateBiz(ctx context.Context, req *UpdateBizRequest) (*BizResponse, error)
	DeleteBiz(ctx context.Context, businessID string) error
	GetBiz(ctx context.Context, businessID string) (*BizResponse, error)
	ListBiz(ctx context.Context, req *ListBizRequest) (*ListBizResponse, error)

	// AlarmType相关方法
	CreateAlarmType(ctx context.Context, req *CreateAlarmTypeRequest) (*AlarmTypeResponse, error)
	UpdateAlarmType(ctx context.Context, req *UpdateAlarmTypeRequest) (*AlarmTypeResponse, error)
	DeleteAlarmType(ctx context.Context, typeID string) error
	GetAlarmType(ctx context.Context, typeID string) (*AlarmTypeResponse, error)
	ListAlarmType(ctx context.Context, req *ListAlarmTypeRequest) (*ListAlarmTypeResponse, error)

	// Tenant相关方法
	CreateTenant(ctx context.Context, req *CreateTenantRequest) (*TenantResponse, error)
	UpdateTenant(ctx context.Context, req *UpdateTenantRequest) (*TenantResponse, error)
	DeleteTenant(ctx context.Context, tenantID string) error
	GetTenant(ctx context.Context, tenantID string) (*TenantResponse, error)
	ListTenant(ctx context.Context, req *ListTenantRequest) (*ListTenantResponse, error)
}

type BizLogicImpl struct{}

var DefaultLogic BizLogic = &BizLogicImpl{}

// Biz相关实现
func (l *BizLogicImpl) CreateBiz(ctx context.Context, req *CreateBizRequest) (*BizResponse, error) {
	// 生成业务ID
	businessID := common.GenerateUUID()
	
	biz := &bizModel.BizTable{
		BusinessID:       businessID,
		Name:             req.Name,
		OriginBusinessID: req.OriginBusinessID,
	}

	err := bizModel.CreateBiz(ctx, biz)
	if err != nil {
		return nil, err
	}

	return &BizResponse{
		ID:               biz.ID,
		BusinessID:       biz.BusinessID,
		Name:             biz.Name,
		OriginBusinessID: biz.OriginBusinessID,
		CreatedAt:        biz.CreatedAt,
		UpdatedAt:        biz.UpdatedAt,
	}, nil
}

func (l *BizLogicImpl) UpdateBiz(ctx context.Context, req *UpdateBizRequest) (*BizResponse, error) {
	err := bizModel.UpdateBizByBizID(ctx, req.BusinessID, req.Name, req.OriginBusinessID)
	if err != nil {
		return nil, err
	}

	// 查询更新后的数据
	filter := bizModel.BizQueryFilter{
		BusinessID: req.BusinessID,
	}
	bizList, err := bizModel.QueryBizBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}
	if len(bizList) == 0 {
		return nil, common.ErrNotFoundf("business not found")
	}

	biz := bizList[0]
	return &BizResponse{
		ID:               biz.ID,
		BusinessID:       biz.BusinessID,
		Name:             biz.Name,
		OriginBusinessID: biz.OriginBusinessID,
		CreatedAt:        biz.CreatedAt,
		UpdatedAt:        biz.UpdatedAt,
	}, nil
}

func (l *BizLogicImpl) DeleteBiz(ctx context.Context, businessID string) error {
	return bizModel.DeleteBiz(ctx, businessID)
}

func (l *BizLogicImpl) GetBiz(ctx context.Context, businessID string) (*BizResponse, error) {
	filter := bizModel.BizQueryFilter{
		BusinessID: businessID,
	}
	bizList, err := bizModel.QueryBizBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}
	if len(bizList) == 0 {
		return nil, common.ErrNotFoundf("business not found")
	}

	biz := bizList[0]
	return &BizResponse{
		ID:               biz.ID,
		BusinessID:       biz.BusinessID,
		Name:             biz.Name,
		OriginBusinessID: biz.OriginBusinessID,
		CreatedAt:        biz.CreatedAt,
		UpdatedAt:        biz.UpdatedAt,
	}, nil
}

func (l *BizLogicImpl) ListBiz(ctx context.Context, req *ListBizRequest) (*ListBizResponse, error) {
	filter := bizModel.BizQueryFilter{
		Page:             req.Page,
		PerPage:          req.PerPage,
		BusinessID:       req.BusinessID,
		Name:             req.Name,
		OriginBusinessID: req.OriginBusinessID,
		Order:            req.Order,
	}

	// 查询数据
	bizList, err := bizModel.QueryBizBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 查询总数
	total, err := bizModel.QueryBizCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 转换数据
	items := make([]*BizResponse, 0, len(bizList))
	for _, biz := range bizList {
		items = append(items, &BizResponse{
			ID:               biz.ID,
			BusinessID:       biz.BusinessID,
			Name:             biz.Name,
			OriginBusinessID: biz.OriginBusinessID,
			CreatedAt:        biz.CreatedAt,
			UpdatedAt:        biz.UpdatedAt,
		})
	}

	return &ListBizResponse{
		Items: items,
		Total: total,
		Page:  req.Page,
		Size:  req.PerPage,
	}, nil
}

// AlarmType相关实现
func (l *BizLogicImpl) CreateAlarmType(ctx context.Context, req *CreateAlarmTypeRequest) (*AlarmTypeResponse, error) {
	// 生成类型ID
	typeID := common.GenerateUUID()
	
	alarmType := &bizModel.AlarmTypeTable{
		TypeID:       typeID,
		BusinessID:   req.BusinessID,
		Name:         req.Name,
		OriginTypeID: req.OriginTypeID,
	}

	err := bizModel.CreateAlarmType(ctx, alarmType)
	if err != nil {
		return nil, err
	}

	return &AlarmTypeResponse{
		ID:           alarmType.ID,
		TypeID:       alarmType.TypeID,
		BusinessID:   alarmType.BusinessID,
		Name:         alarmType.Name,
		OriginTypeID: alarmType.OriginTypeID,
		CreatedAt:    alarmType.CreatedAt,
		UpdatedAt:    alarmType.UpdatedAt,
	}, nil
}

func (l *BizLogicImpl) UpdateAlarmType(ctx context.Context, req *UpdateAlarmTypeRequest) (*AlarmTypeResponse, error) {
	err := bizModel.UpdateAlarmTypeByTypeID(ctx, req.TypeID, req.BusinessID, req.Name, req.OriginTypeID)
	if err != nil {
		return nil, err
	}

	// 查询更新后的数据
	filter := bizModel.AlarmTypeQueryFilter{
		TypeID: req.TypeID,
	}
	alarmTypeList, err := bizModel.QueryAlarmTypeBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}
	if len(alarmTypeList) == 0 {
		return nil, common.ErrNotFoundf("alarm type not found")
	}

	alarmType := alarmTypeList[0]
	return &AlarmTypeResponse{
		ID:           alarmType.ID,
		TypeID:       alarmType.TypeID,
		BusinessID:   alarmType.BusinessID,
		Name:         alarmType.Name,
		OriginTypeID: alarmType.OriginTypeID,
		CreatedAt:    alarmType.CreatedAt,
		UpdatedAt:    alarmType.UpdatedAt,
	}, nil
}

func (l *BizLogicImpl) DeleteAlarmType(ctx context.Context, typeID string) error {
	return bizModel.DeleteAlarmType(ctx, typeID)
}

func (l *BizLogicImpl) GetAlarmType(ctx context.Context, typeID string) (*AlarmTypeResponse, error) {
	filter := bizModel.AlarmTypeQueryFilter{
		TypeID: typeID,
	}
	alarmTypeList, err := bizModel.QueryAlarmTypeBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}
	if len(alarmTypeList) == 0 {
		return nil, common.ErrNotFoundf("alarm type not found")
	}

	alarmType := alarmTypeList[0]
	return &AlarmTypeResponse{
		ID:           alarmType.ID,
		TypeID:       alarmType.TypeID,
		BusinessID:   alarmType.BusinessID,
		Name:         alarmType.Name,
		OriginTypeID: alarmType.OriginTypeID,
		CreatedAt:    alarmType.CreatedAt,
		UpdatedAt:    alarmType.UpdatedAt,
	}, nil
}

func (l *BizLogicImpl) ListAlarmType(ctx context.Context, req *ListAlarmTypeRequest) (*ListAlarmTypeResponse, error) {
	filter := bizModel.AlarmTypeQueryFilter{
		Page:         req.Page,
		PerPage:      req.PerPage,
		TypeID:       req.TypeID,
		BusinessID:   req.BusinessID,
		Name:         req.Name,
		OriginTypeID: req.OriginTypeID,
		Order:        req.Order,
	}

	// 查询数据
	alarmTypeList, err := bizModel.QueryAlarmTypeBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 查询总数
	total, err := bizModel.QueryAlarmTypeCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 转换数据
	items := make([]*AlarmTypeResponse, 0, len(alarmTypeList))
	for _, alarmType := range alarmTypeList {
		items = append(items, &AlarmTypeResponse{
			ID:           alarmType.ID,
			TypeID:       alarmType.TypeID,
			BusinessID:   alarmType.BusinessID,
			Name:         alarmType.Name,
			OriginTypeID: alarmType.OriginTypeID,
			CreatedAt:    alarmType.CreatedAt,
			UpdatedAt:    alarmType.UpdatedAt,
		})
	}

	return &ListAlarmTypeResponse{
		Items: items,
		Total: total,
		Page:  req.Page,
		Size:  req.PerPage,
	}, nil
}

// Tenant相关实现
func (l *BizLogicImpl) CreateTenant(ctx context.Context, req *CreateTenantRequest) (*TenantResponse, error) {
	// 生成租户ID
	tenantID := common.GenerateUUID()

	tenant := &bizModel.TenantTable{
		TenantID:   tenantID,
		Name:       req.Name,
		ExchangeID: req.ExchangeID,
	}

	err := bizModel.CreateTenant(ctx, tenant)
	if err != nil {
		return nil, err
	}

	return &TenantResponse{
		ID:         tenant.ID,
		TenantID:   tenant.TenantID,
		Name:       tenant.Name,
		ExchangeID: tenant.ExchangeID,
		CreatedAt:  tenant.CreatedAt,
		UpdatedAt:  tenant.UpdatedAt,
	}, nil
}

func (l *BizLogicImpl) UpdateTenant(ctx context.Context, req *UpdateTenantRequest) (*TenantResponse, error) {
	err := bizModel.UpdateTenantByTenantID(ctx, req.TenantID, req.Name, req.ExchangeID)
	if err != nil {
		return nil, err
	}

	// 查询更新后的数据
	filter := bizModel.TenantQueryFilter{
		TenantID: req.TenantID,
	}
	tenantList, err := bizModel.QueryTenantBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}
	if len(tenantList) == 0 {
		return nil, common.ErrNotFoundf("tenant not found")
	}

	tenant := tenantList[0]
	return &TenantResponse{
		ID:         tenant.ID,
		TenantID:   tenant.TenantID,
		Name:       tenant.Name,
		ExchangeID: tenant.ExchangeID,
		CreatedAt:  tenant.CreatedAt,
		UpdatedAt:  tenant.UpdatedAt,
	}, nil
}

func (l *BizLogicImpl) DeleteTenant(ctx context.Context, tenantID string) error {
	return bizModel.DeleteTenant(ctx, tenantID)
}

func (l *BizLogicImpl) GetTenant(ctx context.Context, tenantID string) (*TenantResponse, error) {
	filter := bizModel.TenantQueryFilter{
		TenantID: tenantID,
	}
	tenantList, err := bizModel.QueryTenantBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}
	if len(tenantList) == 0 {
		return nil, common.ErrNotFoundf("tenant not found")
	}

	tenant := tenantList[0]
	return &TenantResponse{
		ID:         tenant.ID,
		TenantID:   tenant.TenantID,
		Name:       tenant.Name,
		ExchangeID: tenant.ExchangeID,
		CreatedAt:  tenant.CreatedAt,
		UpdatedAt:  tenant.UpdatedAt,
	}, nil
}

func (l *BizLogicImpl) ListTenant(ctx context.Context, req *ListTenantRequest) (*ListTenantResponse, error) {
	filter := bizModel.TenantQueryFilter{
		Page:       req.Page,
		PerPage:    req.PerPage,
		TenantID:   req.TenantID,
		Name:       req.Name,
		ExchangeID: req.ExchangeID,
		Order:      req.Order,
	}

	// 查询数据
	tenantList, err := bizModel.QueryTenantBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 查询总数
	total, err := bizModel.QueryTenantCountBySeveralConditions(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 转换数据
	items := make([]*TenantResponse, 0, len(tenantList))
	for _, tenant := range tenantList {
		items = append(items, &TenantResponse{
			ID:         tenant.ID,
			TenantID:   tenant.TenantID,
			Name:       tenant.Name,
			ExchangeID: tenant.ExchangeID,
			CreatedAt:  tenant.CreatedAt,
			UpdatedAt:  tenant.UpdatedAt,
		})
	}

	return &ListTenantResponse{
		Items: items,
		Total: total,
		Page:  req.Page,
		Size:  req.PerPage,
	}, nil
}
