/**
 * @note
 * biz handler
 *
 * <AUTHOR>
 * @date 	2025-09-02
 */
package biz

import (
	"net/http"
	"strconv"

	"github.com/kataras/iris/v12"
	"gitlab.docsl.com/security/common"
	bizLogic "gitlab.docsl.com/security/socv2/soc/internal/logic/biz"
)

// BizHandler Biz相关的HTTP处理器
type BizHandler struct{}

// NewBizHandler 创建新的BizHandler实例
func NewBizHandler() *BizHandler {
	return &BizHandler{}
}

// RegisterRoutes 注册路由
func (h *BizHandler) RegisterRoutes(party iris.Party) {
	// Biz相关路由
	bizParty := party.Party("/biz")
	bizParty.Post("/", h.CreateBiz)
	bizParty.Put("/{businessID}", h.UpdateBiz)
	bizParty.Delete("/{businessID}", h.DeleteBiz)
	bizParty.Get("/{businessID}", h.GetBiz)
	bizParty.Get("/", h.ListBiz)

	// AlarmType相关路由
	alarmTypeParty := party.Party("/alarm-type")
	alarmTypeParty.Post("/", h.CreateAlarmType)
	alarmTypeParty.Put("/{typeID}", h.UpdateAlarmType)
	alarmTypeParty.Delete("/{typeID}", h.DeleteAlarmType)
	alarmTypeParty.Get("/{typeID}", h.GetAlarmType)
	alarmTypeParty.Get("/", h.ListAlarmType)

	// Tenant相关路由
	tenantParty := party.Party("/tenant")
	tenantParty.Post("/", h.CreateTenant)
	tenantParty.Put("/{tenantID}", h.UpdateTenant)
	tenantParty.Delete("/{tenantID}", h.DeleteTenant)
	tenantParty.Get("/{tenantID}", h.GetTenant)
	tenantParty.Get("/", h.ListTenant)
}

// Biz相关处理器
func (h *BizHandler) CreateBiz(ctx iris.Context) {
	var req bizLogic.CreateBizRequest
	if err := ctx.ReadJSON(&req); err != nil {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("invalid request body: %v", err))
		return
	}

	resp, err := bizLogic.DefaultLogic.CreateBiz(ctx, &req)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, resp)
}

func (h *BizHandler) UpdateBiz(ctx iris.Context) {
	businessID := ctx.Params().Get("businessID")
	if businessID == "" {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("businessID is required"))
		return
	}

	var req bizLogic.UpdateBizRequest
	if err := ctx.ReadJSON(&req); err != nil {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("invalid request body: %v", err))
		return
	}
	req.BusinessID = businessID

	resp, err := bizLogic.DefaultLogic.UpdateBiz(ctx, &req)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, resp)
}

func (h *BizHandler) DeleteBiz(ctx iris.Context) {
	businessID := ctx.Params().Get("businessID")
	if businessID == "" {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("businessID is required"))
		return
	}

	err := bizLogic.DefaultLogic.DeleteBiz(ctx, businessID)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, nil)
}

func (h *BizHandler) GetBiz(ctx iris.Context) {
	businessID := ctx.Params().Get("businessID")
	if businessID == "" {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("businessID is required"))
		return
	}

	resp, err := bizLogic.DefaultLogic.GetBiz(ctx, businessID)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, resp)
}

func (h *BizHandler) ListBiz(ctx iris.Context) {
	var req bizLogic.ListBizRequest
	
	// 解析查询参数
	if pageStr := ctx.URLParam("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil {
			req.Page = page
		}
	}
	if perPageStr := ctx.URLParam("perPage"); perPageStr != "" {
		if perPage, err := strconv.Atoi(perPageStr); err == nil {
			req.PerPage = perPage
		}
	}
	if originBusinessIDStr := ctx.URLParam("originBusinessID"); originBusinessIDStr != "" {
		if originBusinessID, err := strconv.ParseInt(originBusinessIDStr, 10, 64); err == nil {
			req.OriginBusinessID = originBusinessID
		}
	}
	
	req.BusinessID = ctx.URLParam("businessID")
	req.Name = ctx.URLParam("name")
	req.Order = ctx.URLParam("order")

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PerPage <= 0 {
		req.PerPage = 20
	}

	resp, err := bizLogic.DefaultLogic.ListBiz(ctx, &req)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, resp)
}

// AlarmType相关处理器
func (h *BizHandler) CreateAlarmType(ctx iris.Context) {
	var req bizLogic.CreateAlarmTypeRequest
	if err := ctx.ReadJSON(&req); err != nil {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("invalid request body: %v", err))
		return
	}

	resp, err := bizLogic.DefaultLogic.CreateAlarmType(ctx, &req)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, resp)
}

func (h *BizHandler) UpdateAlarmType(ctx iris.Context) {
	typeID := ctx.Params().Get("typeID")
	if typeID == "" {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("typeID is required"))
		return
	}

	var req bizLogic.UpdateAlarmTypeRequest
	if err := ctx.ReadJSON(&req); err != nil {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("invalid request body: %v", err))
		return
	}
	req.TypeID = typeID

	resp, err := bizLogic.DefaultLogic.UpdateAlarmType(ctx, &req)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, resp)
}

func (h *BizHandler) DeleteAlarmType(ctx iris.Context) {
	typeID := ctx.Params().Get("typeID")
	if typeID == "" {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("typeID is required"))
		return
	}

	err := bizLogic.DefaultLogic.DeleteAlarmType(ctx, typeID)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, nil)
}

func (h *BizHandler) GetAlarmType(ctx iris.Context) {
	typeID := ctx.Params().Get("typeID")
	if typeID == "" {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("typeID is required"))
		return
	}

	resp, err := bizLogic.DefaultLogic.GetAlarmType(ctx, typeID)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, resp)
}

func (h *BizHandler) ListAlarmType(ctx iris.Context) {
	var req bizLogic.ListAlarmTypeRequest
	
	// 解析查询参数
	if pageStr := ctx.URLParam("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil {
			req.Page = page
		}
	}
	if perPageStr := ctx.URLParam("perPage"); perPageStr != "" {
		if perPage, err := strconv.Atoi(perPageStr); err == nil {
			req.PerPage = perPage
		}
	}
	if originTypeIDStr := ctx.URLParam("originTypeID"); originTypeIDStr != "" {
		if originTypeID, err := strconv.ParseInt(originTypeIDStr, 10, 64); err == nil {
			req.OriginTypeID = originTypeID
		}
	}
	
	req.TypeID = ctx.URLParam("typeID")
	req.BusinessID = ctx.URLParam("businessID")
	req.Name = ctx.URLParam("name")
	req.Order = ctx.URLParam("order")

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PerPage <= 0 {
		req.PerPage = 20
	}

	resp, err := bizLogic.DefaultLogic.ListAlarmType(ctx, &req)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, resp)
}

// Tenant相关处理器
func (h *BizHandler) CreateTenant(ctx iris.Context) {
	var req bizLogic.CreateTenantRequest
	if err := ctx.ReadJSON(&req); err != nil {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("invalid request body: %v", err))
		return
	}

	resp, err := bizLogic.DefaultLogic.CreateTenant(ctx, &req)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, resp)
}

func (h *BizHandler) UpdateTenant(ctx iris.Context) {
	tenantID := ctx.Params().Get("tenantID")
	if tenantID == "" {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("tenantID is required"))
		return
	}

	var req bizLogic.UpdateTenantRequest
	if err := ctx.ReadJSON(&req); err != nil {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("invalid request body: %v", err))
		return
	}
	req.TenantID = tenantID

	resp, err := bizLogic.DefaultLogic.UpdateTenant(ctx, &req)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, resp)
}

func (h *BizHandler) DeleteTenant(ctx iris.Context) {
	tenantID := ctx.Params().Get("tenantID")
	if tenantID == "" {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("tenantID is required"))
		return
	}

	err := bizLogic.DefaultLogic.DeleteTenant(ctx, tenantID)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, nil)
}

func (h *BizHandler) GetTenant(ctx iris.Context) {
	tenantID := ctx.Params().Get("tenantID")
	if tenantID == "" {
		common.WriteErrorResponse(ctx, http.StatusBadRequest, common.ErrInvalidParameterf("tenantID is required"))
		return
	}

	resp, err := bizLogic.DefaultLogic.GetTenant(ctx, tenantID)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, resp)
}

func (h *BizHandler) ListTenant(ctx iris.Context) {
	var req bizLogic.ListTenantRequest

	// 解析查询参数
	if pageStr := ctx.URLParam("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil {
			req.Page = page
		}
	}
	if perPageStr := ctx.URLParam("perPage"); perPageStr != "" {
		if perPage, err := strconv.Atoi(perPageStr); err == nil {
			req.PerPage = perPage
		}
	}
	if exchangeIDStr := ctx.URLParam("exchangeID"); exchangeIDStr != "" {
		if exchangeID, err := strconv.ParseInt(exchangeIDStr, 10, 64); err == nil {
			req.ExchangeID = exchangeID
		}
	}

	req.TenantID = ctx.URLParam("tenantID")
	req.Name = ctx.URLParam("name")
	req.Order = ctx.URLParam("order")

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PerPage <= 0 {
		req.PerPage = 20
	}

	resp, err := bizLogic.DefaultLogic.ListTenant(ctx, &req)
	if err != nil {
		common.WriteErrorResponse(ctx, http.StatusInternalServerError, err)
		return
	}

	common.WriteSuccessResponse(ctx, resp)
}
